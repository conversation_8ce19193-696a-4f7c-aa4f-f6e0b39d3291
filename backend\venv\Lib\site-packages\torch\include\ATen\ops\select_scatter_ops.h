#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API select_scatter {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, int64_t, c10::SymInt);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::select_scatter";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "select_scatter(Tensor self, Tensor src, int dim, SymInt index) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Tensor & src, int64_t dim, c10::SymInt index);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & src, int64_t dim, c10::SymInt index);
};

struct TORCH_API select_scatter_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Tensor &, int64_t, c10::SymInt, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::select_scatter";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "select_scatter.out(Tensor self, Tensor src, int dim, SymInt index, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, const at::Tensor & src, int64_t dim, c10::SymInt index, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & src, int64_t dim, c10::SymInt index, at::Tensor & out);
};

}} // namespace at::_ops
