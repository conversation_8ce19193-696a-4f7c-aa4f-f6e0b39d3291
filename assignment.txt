SEC Filings QA Agent - Quantitative Researcher Position

Challenge
Build a question-answering system that analyzes SEC filings to answer complex financial research questions, demonstrating
technical skills, financial domain knowledge, and ability to work with large, unstructured datasets.
Requirements
Data
SEC filings for 10-15 public companies across different sectors
Use sec-api.io API, SEC EDGAR, or other data sources
Multiple filing types relevant for investment research
Reasonable time period for meaningful analysis
System Capabilities
Process financial documents at scale
Synthesize information across multiple documents and time periods
Provide source attribution for all answers
Handle complexity and nuance of financial information

Key Technical Challenges
Query Types to Handle
Ticker-Based: "Apple's risk factors" (AAPL only) vs "Compare Apple and Microsoft revenues" (both tickers)
Temporal: "Apple's 2022 performance" vs "How has revenue guidance changed over time?"
Multi-Dimensional: "Apple's 2022 10-K risk factors" (ticker + time + document type)
Design Decisions
Query processing: Extract tickers, time periods, document types
Document chunking: Preserve structure, attach metadata (ticker, filing_type, date, section)
Retrieval: Semantic vs hybrid search, metadata filtering approach

Sample Evaluation Questions
1. What are the primary revenue drivers for major technology companies, and how have they evolved?
2. Compare R&D spending trends across companies. What insights about innovation investment strategies?
3. Identify significant working capital changes for financial services companies and driving factors.
4. What are the most commonly cited risk factors across industries? How do same-sector companies prioritize differently?
5. How do companies describe climate-related risks? Notable industry differences?
6. Analyze recent executive compensation changes. What trends emerge?
7. What significant insider trading activity occurred? What might this indicate?
8. How are companies positioning regarding AI and automation? Strategic approaches?
9. Identify recent M&A activity. What strategic rationale do companies provide?
10. How do companies describe competitive advantages? What themes emerge?

Evaluation Criteria
Technical Execution: Solution quality, robustness, code clarity, performance
Answer Quality: Accuracy, multi-part question handling, source attribution, uncertainty management

Deliverables
1. Working System: Functional code, setup instructions, example queries
2. Technical Summary (2-4 pages): Approach, challenges addressed, capabilities/limitations, performance, trade-offs

Background & Resources
SEC Filing Types
10-K/10-Q: Financial and business information
8-K: Material events and corporate changes
DEF 14A: Governance and compensation (proxy statements)
Forms 3,4,5: Insider trading activity
Technical Notes
Filings are HTML format with complex nested structures
Document sizes: few pages to several hundred pages
Information spread across multiple filings and time periods
Source attribution is critical for research credibility
Data Sources
SEC.io API: https://sec-api.io/
SEC EDGAR: https://www.sec.gov/edgar
SEC Forms: https://www.sec.gov/forms