# SEC Filings QA Agent - Quantitative Researcher Position

## Challenge

Build a question-answering system that analyzes SEC filings to answer complex financial research questions, demonstrating technical skills, financial domain knowledge, and ability to work with large, unstructured datasets.

## Requirements

### Data
- **Companies**: SEC filings for 10-15 public companies across different sectors
- **Data Sources**: Use sec-api.io API, SEC EDGAR, or other data sources
- **Filing Types**: Multiple filing types relevant for investment research
- **Time Period**: Reasonable time period for meaningful analysis

### System Capabilities
- Process financial documents at scale
- Synthesize information across multiple documents and time periods
- Provide source attribution for all answers
- Handle complexity and nuance of financial information

## Key Technical Challenges

### Query Types to Handle

1. **Ticker-Based Queries**
   - Single ticker: "Apple's risk factors" (AAPL only)
   - Multi-ticker: "Compare Apple and Microsoft revenues" (both tickers)

2. **Temporal Queries**
   - Specific period: "Apple's 2022 performance"
   - Trend analysis: "How has revenue guidance changed over time?"

3. **Multi-Dimensional Queries**
   - Complex filtering: "Apple's 2022 10-K risk factors" (ticker + time + document type)

### Design Decisions

- **Query Processing**: Extract tickers, time periods, document types
- **Document Chunking**: Preserve structure, attach metadata (ticker, filing_type, date, section)
- **Retrieval**: Semantic vs hybrid search, metadata filtering approach

## Sample Evaluation Questions

1. What are the primary revenue drivers for major technology companies, and how have they evolved?

2. Compare R&D spending trends across companies. What insights about innovation investment strategies?

3. Identify significant working capital changes for financial services companies and driving factors.

4. What are the most commonly cited risk factors across industries? How do same-sector companies prioritize differently?

5. How do companies describe climate-related risks? Notable industry differences?

6. Analyze recent executive compensation changes. What trends emerge?

7. What significant insider trading activity occurred? What might this indicate?

8. How are companies positioning regarding AI and automation? Strategic approaches?

9. Identify recent M&A activity. What strategic rationale do companies provide?

10. How do companies describe competitive advantages? What themes emerge?

## Evaluation Criteria

### Technical Execution
- Solution quality
- Robustness
- Code clarity
- Performance

### Answer Quality
- Accuracy
- Multi-part question handling
- Source attribution
- Uncertainty management

## Deliverables

### 1. Working System
- Functional code
- Setup instructions
- Example queries

### 2. Technical Summary (2-4 pages)
- Approach
- Challenges addressed
- Capabilities/limitations
- Performance
- Trade-offs

## Background & Resources

### SEC Filing Types

| Filing Type | Description |
|-------------|-------------|
| **10-K/10-Q** | Financial and business information |
| **8-K** | Material events and corporate changes |
| **DEF 14A** | Governance and compensation (proxy statements) |
| **Forms 3,4,5** | Insider trading activity |

### Technical Notes

- **Format**: Filings are HTML format with complex nested structures
- **Size**: Document sizes range from few pages to several hundred pages
- **Distribution**: Information spread across multiple filings and time periods
- **Attribution**: Source attribution is critical for research credibility

### Data Sources

- **SEC.io API**: https://sec-api.io/
- **SEC EDGAR**: https://www.sec.gov/edgar
- **SEC Forms**: https://www.sec.gov/forms

## Getting Started

This assignment requires building a comprehensive system that can:
1. Ingest and process SEC filings from multiple companies
2. Parse and structure the unstructured financial data
3. Implement intelligent query processing and retrieval
4. Provide accurate, well-sourced answers to complex financial questions
5. Handle various query types and temporal analysis

The system should demonstrate both technical proficiency and deep understanding of financial data analysis requirements.
